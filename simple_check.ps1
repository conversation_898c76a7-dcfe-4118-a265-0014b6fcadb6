# 简化版外网访问检查脚本

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "外网访问诊断 - StudentsCMSSP" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

# 1. 获取外网IP
Write-Host "`n1. 网络配置检查" -ForegroundColor Yellow
try {
    $externalIP = (Invoke-WebRequest -Uri "http://ifconfig.me" -TimeoutSec 10).Content.Trim()
    Write-Host "外网IP地址: $externalIP" -ForegroundColor Green
} catch {
    Write-Host "无法获取外网IP地址" -ForegroundColor Red
    $externalIP = "Unknown"
}

# 2. 检查端口状态
Write-Host "`n2. 端口状态检查" -ForegroundColor Yellow
$port80 = netstat -ano | Select-String ":80.*LISTENING"
$port8080 = netstat -ano | Select-String ":8080.*LISTENING"

if ($port80) {
    Write-Host "端口 80 (IIS): 正在监听" -ForegroundColor Green
} else {
    Write-Host "端口 80 (IIS): 未监听" -ForegroundColor Red
}

if ($port8080) {
    Write-Host "端口 8080 (Flask): 正在监听" -ForegroundColor Green
} else {
    Write-Host "端口 8080 (Flask): 未监听" -ForegroundColor Red
}

# 3. 测试本地连接
Write-Host "`n3. 本地连接测试" -ForegroundColor Yellow

# 测试Flask应用
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8080" -TimeoutSec 5 -ErrorAction Stop
    Write-Host "Flask应用 (8080): 响应正常 (状态码: $($response.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "Flask应用 (8080): 连接失败" -ForegroundColor Red
}

# 测试IIS代理
try {
    $response = Invoke-WebRequest -Uri "http://localhost" -TimeoutSec 5 -ErrorAction Stop
    Write-Host "IIS代理 (80): 响应正常 (状态码: $($response.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "IIS代理 (80): 连接失败" -ForegroundColor Red
}

# 4. 测试外网连接
Write-Host "`n4. 外网连接测试" -ForegroundColor Yellow
if ($externalIP -ne "Unknown") {
    try {
        $tcpTest = Test-NetConnection -ComputerName $externalIP -Port 80 -WarningAction SilentlyContinue
        if ($tcpTest.TcpTestSucceeded) {
            Write-Host "外网80端口: 可访问" -ForegroundColor Green
        } else {
            Write-Host "外网80端口: 不可访问" -ForegroundColor Red
        }
    } catch {
        Write-Host "外网连接测试失败" -ForegroundColor Red
    }
}

# 5. 域名解析测试
Write-Host "`n5. 域名解析测试" -ForegroundColor Yellow
$domain = "xuexiaost.com"
try {
    $dnsResult = Resolve-DnsName -Name $domain -ErrorAction Stop
    $resolvedIP = $dnsResult.IPAddress
    Write-Host "$domain -> $resolvedIP" -ForegroundColor Green
    
    if ($resolvedIP -eq $externalIP) {
        Write-Host "域名指向正确的IP地址" -ForegroundColor Green
    } else {
        Write-Host "域名指向的IP ($resolvedIP) 与当前外网IP ($externalIP) 不匹配" -ForegroundColor Yellow
    }
} catch {
    Write-Host "$domain: 解析失败" -ForegroundColor Red
}

# 6. 总结
Write-Host "`n========================================" -ForegroundColor Cyan
Write-Host "诊断总结" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

Write-Host "`n当前访问方式:" -ForegroundColor White
Write-Host "- 本地访问: http://localhost" -ForegroundColor Gray
Write-Host "- IP访问: http://$externalIP" -ForegroundColor Gray
Write-Host "- 域名访问: http://xuexiaost.com (备案完成后)" -ForegroundColor Gray

Write-Host "`n如果外网仍无法访问，请检查:" -ForegroundColor Yellow
Write-Host "1. 云服务器安全组是否开放80端口" -ForegroundColor White
Write-Host "2. 运营商是否封禁80端口" -ForegroundColor White
Write-Host "3. Flask应用是否正常运行" -ForegroundColor White
Write-Host "4. IIS反向代理配置是否正确" -ForegroundColor White
