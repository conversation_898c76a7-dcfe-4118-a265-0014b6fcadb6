# PowerShell脚本：检查外网访问状态
# 诊断为什么外网无法访问本项目

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "外网访问诊断工具 - StudentsCMSSP" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

# 1. 检查网络配置
Write-Host "`n1. 网络配置检查" -ForegroundColor Yellow
Write-Host "----------------------------------------" -ForegroundColor Gray

# 获取外网IP
try {
    $externalIP = (Invoke-WebRequest -Uri "http://ifconfig.me" -TimeoutSec 10).Content.Trim()
    Write-Host "✓ 外网IP地址: $externalIP" -ForegroundColor Green
} catch {
    Write-Host "✗ 无法获取外网IP地址" -ForegroundColor Red
    $externalIP = "未知"
}

# 获取内网IP
$internalIP = (Get-NetIPAddress -AddressFamily IPv4 | Where-Object {$_.IPAddress -ne "127.0.0.1"}).IPAddress
Write-Host "✓ 内网IP地址: $internalIP" -ForegroundColor Green

# 2. 检查端口状态
Write-Host "`n2. 端口状态检查" -ForegroundColor Yellow
Write-Host "----------------------------------------" -ForegroundColor Gray

$ports = @(
    @{Port=80; Name="IIS (HTTP)"; Required=$true},
    @{Port=8080; Name="Flask应用"; Required=$true},
    @{Port=443; Name="HTTPS"; Required=$false}
)

foreach ($portInfo in $ports) {
    $port = $portInfo.Port
    $name = $portInfo.Name
    $required = $portInfo.Required
    
    $listening = netstat -ano | Select-String ":$port.*LISTENING"
    if ($listening) {
        Write-Host "✓ 端口 $port ($name): 正在监听" -ForegroundColor Green
    } else {
        if ($required) {
            Write-Host "✗ 端口 $port ($name): 未监听 [必需]" -ForegroundColor Red
        } else {
            Write-Host "⚠ 端口 $port ($name): 未监听 [可选]" -ForegroundColor Yellow
        }
    }
}

# 3. 检查IIS状态
Write-Host "`n3. IIS服务状态检查" -ForegroundColor Yellow
Write-Host "----------------------------------------" -ForegroundColor Gray

try {
    Import-Module WebAdministration -ErrorAction Stop
    $websites = Get-Website
    
    foreach ($site in $websites) {
        $status = if ($site.State -eq "Started") { "OK" } else { "ERROR" }
        $color = if ($site.State -eq "Started") { "Green" } else { "Red" }
        Write-Host "$status Website: $($site.Name) - Status: $($site.State)" -ForegroundColor $color
    }
} catch {
    Write-Host "✗ 无法检查IIS状态: $($_.Exception.Message)" -ForegroundColor Red
}

# 4. 检查防火墙规则
Write-Host "`n4. 防火墙规则检查" -ForegroundColor Yellow
Write-Host "----------------------------------------" -ForegroundColor Gray

$httpRule = Get-NetFirewallRule -DisplayName "*HTTP*" | Where-Object {$_.Enabled -eq $true -and $_.Direction -eq "Inbound"}
if ($httpRule) {
    Write-Host "OK HTTP inbound rules enabled" -ForegroundColor Green
} else {
    Write-Host "ERROR HTTP inbound rules not enabled" -ForegroundColor Red
}

# 5. 测试本地连接
Write-Host "`n5. 本地连接测试" -ForegroundColor Yellow
Write-Host "----------------------------------------" -ForegroundColor Gray

# Test Flask application
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8080" -TimeoutSec 5 -ErrorAction Stop
    Write-Host "OK Flask app (8080): Response normal (Status: $($response.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "ERROR Flask app (8080): Connection failed - $($_.Exception.Message)" -ForegroundColor Red
}

# Test IIS proxy
try {
    $response = Invoke-WebRequest -Uri "http://localhost" -TimeoutSec 5 -ErrorAction Stop
    Write-Host "OK IIS proxy (80): Response normal (Status: $($response.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "ERROR IIS proxy (80): Connection failed - $($_.Exception.Message)" -ForegroundColor Red
}

# 6. 测试外网连接
Write-Host "`n6. 外网连接测试" -ForegroundColor Yellow
Write-Host "----------------------------------------" -ForegroundColor Gray

if ($externalIP -ne "Unknown") {
    try {
        $tcpTest = Test-NetConnection -ComputerName $externalIP -Port 80 -WarningAction SilentlyContinue
        if ($tcpTest.TcpTestSucceeded) {
            Write-Host "OK External port 80: Accessible" -ForegroundColor Green
        } else {
            Write-Host "ERROR External port 80: Not accessible" -ForegroundColor Red
        }
    } catch {
        Write-Host "ERROR External connection test failed: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 7. 域名解析测试
Write-Host "`n7. 域名解析测试" -ForegroundColor Yellow
Write-Host "----------------------------------------" -ForegroundColor Gray

$domains = @("xuexiaost.com", "www.xuexiaost.com")
foreach ($domain in $domains) {
    try {
        $dnsResult = Resolve-DnsName -Name $domain -ErrorAction Stop
        $resolvedIP = $dnsResult.IPAddress
        Write-Host "OK $domain -> $resolvedIP" -ForegroundColor Green

        if ($resolvedIP -eq $externalIP) {
            Write-Host "  OK Domain points to correct IP address" -ForegroundColor Green
        } else {
            Write-Host "  WARNING Domain IP ($resolvedIP) does not match current external IP ($externalIP)" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "ERROR ${domain}: DNS resolution failed - $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 8. 总结和建议
Write-Host "`n8. 诊断总结" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan

Write-Host "`nCurrent access methods:" -ForegroundColor White
Write-Host "- Local access: http://localhost" -ForegroundColor Gray
Write-Host "- IP access: http://$externalIP" -ForegroundColor Gray
Write-Host "- Domain access: http://xuexiaost.com (after ICP filing)" -ForegroundColor Gray

Write-Host "`nIf external access still fails, check:" -ForegroundColor Yellow
Write-Host "1. Cloud server security group allows port 80" -ForegroundColor White
Write-Host "2. ISP blocks port 80" -ForegroundColor White
Write-Host "3. Flask application is running normally" -ForegroundColor White
Write-Host "4. IIS reverse proxy configuration is correct" -ForegroundColor White
