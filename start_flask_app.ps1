# PowerShell脚本：启动Flask应用
# 确保Flask应用在8080端口正常运行

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "启动Flask应用 - StudentsCMSSP" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

# 检查虚拟环境
if (Test-Path "venv\Scripts\activate.ps1") {
    Write-Host "✓ 激活虚拟环境..." -ForegroundColor Green
    & "venv\Scripts\activate.ps1"
} else {
    Write-Host "⚠ 虚拟环境不存在，使用系统Python" -ForegroundColor Yellow
}

# 检查端口8080是否被占用
$port8080 = netstat -ano | Select-String ":8080.*LISTENING"
if ($port8080) {
    Write-Host "⚠ 端口8080已被占用，正在终止现有进程..." -ForegroundColor Yellow
    $pid = ($port8080 -split '\s+')[-1]
    Stop-Process -Id $pid -Force -ErrorAction SilentlyContinue
    Start-Sleep -Seconds 2
}

# 检查依赖
Write-Host "检查Python依赖..." -ForegroundColor Yellow
try {
    python -c "import flask; print('Flask版本:', flask.__version__)"
    Write-Host "✓ Flask已安装" -ForegroundColor Green
} catch {
    Write-Host "✗ Flask未安装，正在安装依赖..." -ForegroundColor Red
    pip install -r requirements.txt
}

# 启动Flask应用
Write-Host "`n正在启动Flask应用..." -ForegroundColor Yellow
Write-Host "本地访问: http://localhost:8080" -ForegroundColor White
Write-Host "外网访问: http://**************" -ForegroundColor White
Write-Host "域名访问: http://xuexiaost.com (备案完成后)" -ForegroundColor White
Write-Host "`n按 Ctrl+C 停止应用" -ForegroundColor Gray

# 启动应用
python run.py
